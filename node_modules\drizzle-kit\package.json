{"name": "drizzle-kit", "version": "0.31.4", "homepage": "https://orm.drizzle.team", "keywords": ["drizzle", "orm", "pg", "mysql", "singlestore", "postgresql", "postgres", "sqlite", "database", "sql", "typescript", "ts", "drizzle-kit", "migrations", "schema"], "publishConfig": {"provenance": true}, "repository": {"type": "git", "url": "git+https://github.com/drizzle-team/drizzle-orm.git"}, "author": "Drizzle Team", "license": "MIT", "bin": {"drizzle-kit": "./bin.cjs"}, "scripts": {"api": "tsx ./dev/api.ts", "migrate:old": "drizzle-kit generate:mysql", "cli": "tsx ./src/cli/index.ts", "test": "pnpm tsc && TEST_CONFIG_PATH_PREFIX=./tests/cli/ vitest", "build": "rm -rf ./dist && tsx build.ts && cp package.json dist/ && attw --pack dist", "build:dev": "rm -rf ./dist && tsx build.dev.ts && tsc -p tsconfig.cli-types.json && chmod +x ./dist/index.cjs", "pack": "cp package.json README.md dist/ && (cd dist && npm pack --pack-destination ..) && rm -f package.tgz && mv *.tgz package.tgz", "tsc": "tsc -p tsconfig.build.json --noEmit", "publish": "npm publish package.tgz"}, "dependencies": {"@drizzle-team/brocli": "^0.10.2", "@esbuild-kit/esm-loader": "^2.5.5", "esbuild": "^0.25.4", "esbuild-register": "^3.5.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.3", "@aws-sdk/client-rds-data": "^3.556.0", "@cloudflare/workers-types": "^4.20230518.0", "@electric-sql/pglite": "^0.2.12", "@hono/node-server": "^1.9.0", "@hono/zod-validator": "^0.2.1", "@libsql/client": "^0.10.0", "@neondatabase/serverless": "^0.9.1", "@originjs/vite-plugin-commonjs": "^1.0.3", "@planetscale/database": "^1.16.0", "@types/better-sqlite3": "^7.6.13", "@types/dockerode": "^3.3.28", "@types/glob": "^8.1.0", "@types/json-diff": "^1.0.3", "@types/micromatch": "^4.0.9", "@types/minimatch": "^5.1.2", "@types/node": "^18.11.15", "@types/pg": "^8.10.7", "@types/pluralize": "^0.0.33", "@types/semver": "^7.5.5", "@types/uuid": "^9.0.8", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vercel/postgres": "^0.8.0", "ava": "^5.1.0", "better-sqlite3": "^11.9.1", "bun-types": "^0.6.6", "camelcase": "^7.0.1", "chalk": "^5.2.0", "commander": "^12.1.0", "dockerode": "^4.0.6", "dotenv": "^16.0.3", "drizzle-kit": "0.25.0-b1faa33", "drizzle-orm": "workspace:./drizzle-orm/dist", "env-paths": "^3.0.0", "esbuild-node-externals": "^1.9.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "gel": "^2.0.0", "get-port": "^6.1.2", "glob": "^8.1.0", "hanji": "^0.0.5", "hono": "^4.7.9", "json-diff": "1.0.6", "micromatch": "^4.0.8", "minimatch": "^7.4.3", "mysql2": "3.14.1", "node-fetch": "^3.3.2", "ohm-js": "^17.1.0", "pg": "^8.11.5", "pluralize": "^8.0.0", "postgres": "^3.4.4", "prettier": "^3.5.3", "semver": "^7.7.2", "superjson": "^2.2.1", "tsup": "^8.3.5", "tsx": "^3.12.1", "typescript": "^5.6.3", "uuid": "^9.0.1", "vite-tsconfig-paths": "^4.3.2", "vitest": "^3.1.3", "ws": "^8.18.2", "zod": "^3.20.2", "zx": "^8.3.2"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}, "types": "./index.d.mts", "default": "./index.mjs"}, "./api": {"import": {"types": "./api.d.mts", "default": "./api.mjs"}, "require": {"types": "./api.d.ts", "default": "./api.js"}, "types": "./api.d.mts", "default": "./api.mjs"}}}