{"name": "better-auth-tutorial", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "better-auth": "^1.2.12", "dotenv": "^17.0.0", "drizzle-orm": "^0.44.2", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "tailwindcss": "^4", "typescript": "^5"}}